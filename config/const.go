package config

const (
	// KeyCtxUserInfo 上下文用户信息键
	KeyCtxUserInfo = "user_info"
)

const (
	// BatchSize 批量获取数量
	BatchSize = 1000

	// ActivityRewardEmailTemplate 活动奖励邮件模板ID
	ActivityRewardEmailTemplate = 10000002
)

// RewardDeliveryMethod 奖励发放方式
type RewardDeliveryMethod int32

const (
	// RewardDeliveryDirect 直接发放（手动领奖）
	RewardDeliveryDirect RewardDeliveryMethod = iota

	// RewardDeliveryMail 邮件发放（自动领奖）
	RewardDeliveryMail
)
