package logic_default

import (
	"activitysrv/config"
	"context"
	"fmt"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
)

// UserContextManager 用户上下文管理器
// 提供高质量的用户信息缓存和管理功能
type UserContextManager struct {
	ctx      context.Context
	userInfo *commonPB.RichUserInfo
	loaded   bool
}

// NewUserContextManager 创建用户上下文管理器
func NewUserContextManager(ctx context.Context) *UserContextManager {
	return &UserContextManager{
		ctx:    ctx,
		loaded: false,
	}
}

// EnsureUserInfo 确保用户信息已加载（懒加载模式）
func (m *UserContextManager) EnsureUserInfo() error {
	if m.loaded {
		return nil
	}

	// 先检查 context 中是否已有用户信息
	if userInfo, ok := m.ctx.Value(config.KeyCtxUserInfo).(*commonPB.RichUserInfo); ok {
		m.userInfo = userInfo
		m.loaded = true
		return nil
	}

	// 获取用户信息
	opt := interceptor.GetRPCOptions(m.ctx)
	userInfoRsp, err := crpc_user.RpcGetPlayerInfo(m.ctx, opt.ProductId, opt.PlayerId)
	if err != nil {
		logrus.Warnf("RpcGetPlayerInfo fail: %v productId:%d playerId:%d", err, opt.ProductId, opt.PlayerId)
		return fmt.Errorf("failed to get player info: %w", err)
	}

	userInfo := userInfoRsp.GetRichUserInfo()
	if userInfo == nil {
		logrus.Warnf("RpcGetPlayerInfo returned nil userInfo: productId:%d playerId:%d", opt.ProductId, opt.PlayerId)
		return fmt.Errorf("player info is nil")
	}

	m.userInfo = userInfo
	m.loaded = true

	// 更新 context
	m.ctx = context.WithValue(m.ctx, config.KeyCtxUserInfo, userInfo)

	return nil
}

// GetUserInfo 获取用户信息
func (m *UserContextManager) GetUserInfo() (*commonPB.RichUserInfo, error) {
	if err := m.EnsureUserInfo(); err != nil {
		return nil, err
	}
	return m.userInfo, nil
}

// GetContext 获取更新后的 context
func (m *UserContextManager) GetContext() context.Context {
	return m.ctx
}

// CheckRoleLevel 检查角色等级
func (m *UserContextManager) CheckRoleLevel(requiredLevel int32) (bool, error) {
	userInfo, err := m.GetUserInfo()
	if err != nil {
		return false, err
	}

	roleLev := userInfo.GetBriefUserInfo().GetLev()
	return roleLev >= requiredLevel, nil
}

// GetRoleLevel 获取角色等级
func (m *UserContextManager) GetRoleLevel() (int32, error) {
	userInfo, err := m.GetUserInfo()
	if err != nil {
		return 0, err
	}

	return userInfo.GetBriefUserInfo().GetLev(), nil
}

// IsLoaded 检查用户信息是否已加载
func (m *UserContextManager) IsLoaded() bool {
	return m.loaded
}
