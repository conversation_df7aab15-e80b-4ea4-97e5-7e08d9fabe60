package logic_default

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"activitysrv/internal/model"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"time"
)

// isActivityActive 检查活动是否活跃
func isActivityActive(cfg *cmodel.Activity) bool {
	// 检查活动时间
	now := time.Now().Unix()
	if now < cfg.OpenAt || (cfg.CloseAt > 0 && now > cfg.CloseAt) {
		return false
	}

	return true
}

// hasActivityMetrics 检查事件是否包含活动相关指标
func hasActivityMetrics(event *commonPB.EventCommon, cfg *cmodel.Activity) bool {
	if _, exists := event.IntData[cfg.Target]; exists {
		return true
	}
	return false
}

// filterActiveActivitiesForEvent 筛选当前开启且支持该事件类型的活动
func filterActiveActivitiesForEvent(allActivities map[int64]*cmodel.Activity, event *commonPB.EventCommon) []*cmodel.Activity {
	var activeActivities []*cmodel.Activity

	for _, activityCfg := range allActivities {
		// 检查活动是否活跃
		if !isActivityActive(activityCfg) {
			continue
		}

		// 检查活动指标是否包含在事件中
		if !hasActivityMetrics(event, activityCfg) {
			continue
		}

		activeActivities = append(activeActivities, activityCfg)
	}

	return activeActivities
}

// CheckAndCreateCycleIfNeeded 检查并在需要时创建新周期 - 业务逻辑函数
func CheckAndCreateCycleIfNeeded(ctx context.Context, activityCfg *cmodel.Activity) (*model.ActivityCycle, error) {
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityCfg.Id)

	if err != nil {
		return nil, err
	}

	// 如果没有当前周期或当前周期已过期，创建新周期
	if currentCycle == nil || currentCycle.IsExpired() {
		return CreateNewCycle(ctx, activityCfg)
	}

	return currentCycle, nil
}

// calculateCycleIdAndTime 计算周期ID和时间
func calculateCycleIdAndTime(activityCfg *cmodel.Activity, now int64) (newCycle *model.ActivityCycle, err error) {
	var cycleId int32
	var startTime, endTime int64

	if !activityCfg.IsLoop {
		// 非循环活动：周期固定为1，时间就是活动时间
		cycleId = 1
		startTime = activityCfg.OpenAt
		if activityCfg.CloseAt > 0 {
			endTime = activityCfg.CloseAt
		} else {
			cycleDays := activityCfg.CycleDays
			if cycleDays <= 0 {
				cycleDays = 7 // 默认7天
			}
			endTime = startTime + int64(cycleDays)*24*3600
		}
		logrus.Debugf("non-loop activity: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
			activityCfg.Id, cycleId, startTime, endTime)
		return model.NewActivityCycle(cycleId, activityCfg.CycleDays, startTime, endTime), nil
	}

	// 循环活动：根据CycleDays计算当前周期
	if activityCfg.CycleDays <= 0 {
		return nil, fmt.Errorf("invalid cycle days configuration for loop activity: activityId=%d, cycleDays=%d",
			activityCfg.Id, activityCfg.CycleDays)
	}

	cycleSeconds := int64(activityCfg.CycleDays) * 24 * 3600
	elapsedSeconds := now - activityCfg.OpenAt
	cycleId = int32(elapsedSeconds/cycleSeconds) + 1
	if cycleId <= 0 {
		cycleId = 1
	}

	// 计算周期时间
	startTime = activityCfg.OpenAt + int64(cycleId-1)*cycleSeconds
	endTime = startTime + cycleSeconds

	// 确保不超出活动结束时间
	if activityCfg.CloseAt > 0 && endTime > activityCfg.CloseAt {
		endTime = activityCfg.CloseAt
		// 如果当前周期会超出活动结束时间，调整为最大周期
		maxCycleId := int32((activityCfg.CloseAt-activityCfg.OpenAt)/cycleSeconds) + 1
		if cycleId > maxCycleId {
			cycleId = maxCycleId
		}
	}

	// 验证时间合理性
	if startTime >= endTime {
		return nil, fmt.Errorf("calculated cycle time is invalid: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
			activityCfg.Id, cycleId, startTime, endTime)
	}

	logrus.Debugf("loop activity: activityId=%d, cycleId=%d, cycleDays=%d, startTime=%d, endTime=%d",
		activityCfg.Id, cycleId, activityCfg.CycleDays, startTime, endTime)

	return model.NewActivityCycle(cycleId, activityCfg.CycleDays, startTime, endTime), nil
}

// isMetricRelevantToActivity 检查指标是否与活动相关
func isMetricRelevantToActivity(metricType int32, activityCfg *cmodel.Activity) bool {
	if metricType == activityCfg.Target {
		return true
	}
	return false
}

// validateActivityCycle 校验活动时间和周期有效性
func validateActivityCycle(ctx context.Context, activityId int64, cycleId int32) (*model.ActivityCycle, error) {
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, fmt.Errorf("failed to get current cycle: %w", err)
	}
	if currentCycle == nil {
		return nil, fmt.Errorf("no active activity cycle currently")
	}

	// 检查周期是否有效
	if cycleId != currentCycle.CycleId && cycleId != currentCycle.CycleId-1 {
		return nil, fmt.Errorf("rewards for cycle %d have expired, only current cycle and previous cycle rewards are supported", cycleId)
	}

	return currentCycle, nil
}

// checkRoleLev 检查角色等级
func checkRoleLev(ctx context.Context, target int32) bool {
	userInfo, ok := ctx.Value(config.KeyCtxUserInfo).(*commonPB.RichUserInfo)
	if !ok {
		// 查询用户信息
		opt := interceptor.GetRPCOptions(ctx)
		userInfoRsp, err := crpc_user.RpcGetPlayerInfo(ctx, opt.ProductId, opt.PlayerId)
		// 查询用户信息失败
		if err != nil {
			logrus.Warnf("RpcGetPlayerInfo fail: %v productId:%d playerId:%d", err, opt.ProductId, opt.PlayerId)
			return false
		}
		userInfo = userInfoRsp.GetRichUserInfo()
		if userInfo == nil {
			// 查询用户信息失败
			logrus.Warnf("RpcGetPlayerInfo fail: %v productId:%d playerId:%d", err, opt.ProductId, opt.PlayerId)
			return false
		}
		ctx = context.WithValue(ctx, config.KeyCtxUserInfo, userInfo)
	}
	roleLev := userInfo.GetBriefUserInfo().GetLev()
	return roleLev >= target
}
