package logic_default

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"activitysrv/internal/model"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
	"github.com/sirupsen/logrus"
	"time"
)

// processActivityEvent 处理单个活动的事件
func processActivityEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon, activityCfg *cmodel.Activity) error {
	// 1. 玩家等级和周期管理检查
	currentCycle, err := CheckAndCreateCycleIfNeeded(ctx, activityCfg)
	if err != nil {
		return fmt.Errorf("failed to check or create activity cycle: activityId=%d, %w", activityCfg.Id, err)
	}
	if !checkRoleLev(ctx, activityCfg.Lev) {
		return nil
	}

	userInfo, ok := ctx.Value(config.KeyCtxUserInfo).(*commonPB.RichUserInfo)
	logrus.Debugf("processActivityEvent userInfo=%+v, ok=%v", userInfo, ok)

	// 2. 加锁
	unlock := dlm.LockKey(config.UserActivityLockKey(activityCfg.Id, playerId))
	defer unlock()

	// 3. 获取用户数据
	currentUserData, err := dao_activity.GetUserData(ctx, activityCfg.Id, playerId, currentCycle.CycleId)
	if err != nil {
		return fmt.Errorf("failed to get user data: %w", err)
	}

	// 4. 更新用户指标 - 使用活动指标处理器
	processor := GetActivityMetricsProcessor(activityCfg.Id)
	err = processor.ProcessMetrics(currentUserData, event, activityCfg)
	if err != nil {
		return fmt.Errorf("failed to update activity metrics: %w", err)
	}

	// 5. 保存更新后的用户数据
	currentUserData.MetricsType = activityCfg.Update
	err = dao_activity.SaveUserData(ctx, playerId, activityCfg, currentCycle, currentUserData)
	if err != nil {
		return err
	}

	// 6. 添加玩家到周期记录（成功处理事件后）
	_ = dao_activity.AddPlayerToCycle(ctx, activityCfg.Id, currentCycle.CycleId, playerId)

	return nil
}

// CreateNewCycle 创建新的活动周期 - 业务逻辑函数
func CreateNewCycle(ctx context.Context, activityCfg *cmodel.Activity) (*model.ActivityCycle, error) {
	now := time.Now().Unix()

	// 1. 验证活动时间配置
	if now < activityCfg.OpenAt {
		return nil, fmt.Errorf("activity has not started yet: activityId=%d, openAt=%d, now=%d",
			activityCfg.Id, activityCfg.OpenAt, now)
	}
	if activityCfg.CloseAt > 0 && now > activityCfg.CloseAt {
		return nil, fmt.Errorf("activity has ended: activityId=%d, closeAt=%d, now=%d",
			activityCfg.Id, activityCfg.CloseAt, now)
	}

	// 2. 计算周期ID和时间
	newCycle, err := calculateCycleIdAndTime(activityCfg, now)
	if err != nil {
		return nil, err
	}

	// 3. 保存到Redis
	if err = dao_activity.SaveCurrentCycle(ctx, activityCfg.Id, newCycle); err != nil {
		return nil, fmt.Errorf("failed to save current cycle: %w", err)
	}

	logrus.Debugf("successfully created new activity cycle: activityId=%d, cycleId=%d, startTime=%d, endTime=%d",
		activityCfg.Id, newCycle.CycleId, newCycle.StartTime, newCycle.EndTime)

	return newCycle, nil
}
