package logic_default

import (
	"activitysrv/config"
	"activitysrv/internal/dao/dao_activity"
	"context"
	"fmt"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/dlm"
)

// HandleEvent 处理事件 - 实现ActivityHandler接口
func HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	entry := logx.NewLogEntry(ctx)

	// 1. 获取所有活动配置
	allActivities := cmodel.GetAllActivity(consul_config.WithGrpcCtx(ctx))
	if allActivities == nil {
		entry.Debugf("activity configuration not found")
		return nil
	}

	// 2. 筛选当前开启且支持该事件类型的活动
	activeActivities := filterActiveActivitiesForEvent(allActivities, event)
	if len(activeActivities) == 0 {
		entry.Debugf("no active activities need to handle this event: eventType=%v", event.EventType)
		return nil
	}

	// 3. 预加载用户信息到 context（一次性获取，避免重复RPC调用）
	ctx, err := ensureUserInfoInContext(ctx)
	if err != nil {
		entry.Errorf("failed to load user info: %v", err)
		return err
	}

	// 4. 处理所有匹配的活动
	for _, activityCfg := range activeActivities {
		err := processActivityEvent(ctx, playerId, event, activityCfg)
		if err != nil {
			entry.Errorf("failed to process activity event: activityId=%d, err=%v", activityCfg.Id, err)
		}
	}

	return nil
}

// GetProgress 获取活动进度 - 实现ActivityHandler接口
func GetProgress(ctx context.Context, playerId uint64, req *activityPB.GetActivityProgressReq) ([]*activityPB.ActivityProgress, error) {
	entry := logx.NewLogEntry(ctx)

	activityId := int64(req.GetActivityId())
	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_ACTIVITY_NOT_EXIST, "activity configuration not found")
	}

	// 获取当前周期
	currentCycle, err := CheckAndCreateCycleIfNeeded(ctx, activityCfg)
	if err != nil {
		return nil, fmt.Errorf("failed to get current cycle: %w", err)
	}

	// 获取当前周期的用户数据
	currentUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, currentCycle.CycleId)
	if err != nil {
		return nil, fmt.Errorf("failed to get current cycle user data: %w", err)
	}

	var res []*activityPB.ActivityProgress
	// 构建进度数据
	progress := currentUserData.ToActivityProgress(currentCycle.CycleId, currentCycle.EndTime)
	res = append(res, progress)

	// 获取上个周期数据（如果存在）
	if currentCycle.CycleId > 1 {
		previousCycleId := currentCycle.CycleId - 1
		previousUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, previousCycleId)
		if err != nil {
			entry.Warnf("failed to get previous cycle user data: %v", err)
		} else {
			previousCycle := previousUserData.ToActivityProgress(previousCycleId, 0)
			res = append(res, previousCycle)
		}
	}

	entry.Debugf("successfully retrieved activity progress: activityId=%d, playerId=%d, currentCycleId=%d",
		req.GetActivityId(), playerId, currentCycle.CycleId)

	return res, nil
}

// ClaimReward 领取奖励
func ClaimReward(ctx context.Context, playerId uint64, req *activityPB.ClaimActivityRewardReq) (*activityPB.ClaimActivityRewardRsp, error) {
	activityId := int64(req.GetActivityId())
	cycleId := req.GetCycleId()

	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, protox.CodeError(commonPB.ErrCode_ERR_ACTIVITY_NOT_EXIST, "activity configuration not found")
	}

	// 校验活动时间和周期有效性
	currentCycle, err := validateActivityCycle(ctx, activityId, cycleId)
	if err != nil {
		return nil, err
	}

	// 加锁
	unlock := dlm.LockKey(config.UserActivityLockKey(activityId, playerId))
	defer unlock()

	// 获取用户数据
	userData, err := dao_activity.GetUserData(ctx, activityId, playerId, cycleId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user data: %w", err)
	}

	// 执行通用领奖逻辑（手动领奖使用直接发放）
	claimedStages, err := processRewardClaim(ctx, playerId, userData, activityCfg, currentCycle, config.RewardDeliveryDirect)
	if err != nil {
		return nil, err
	}

	// 成功领取奖励后，从周期记录中移除玩家
	_ = dao_activity.RemovePlayerFromCycle(ctx, activityId, cycleId, playerId)

	res := userData.ToClaimActivityRewardRsp(claimedStages, cycleId)

	return res, nil
}
